import { Absolute<PERSON>oader, StepLayout } from "@common";
import { LeavingGuard } from "@common/LeavingGuard/LeavingGuard";
import { IProspect } from "@features/acquisition/IAcquisition";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router-dom";
import { KeyedMutator } from "swr";
import { useScheduleInstallationStep } from "./useScheduleInstallationStep";
import { ITigoSalesProspectLead } from "@modules/tigoSalesFacade/interfaces/models/ITigoSalesProspectLead";
import { EAddressTypeValues } from "@modules/tigoSalesFacade/enums/EAddressTypeValues";
import { IPostpayCartModel } from "../../IPostpay";
import ScheduleForm from "@common/ScheduleForm/ScheduleForm";
import { useState, useEffect } from "react";
import ReScheduleConfirmationModal from "@common/styleComponents/OrderConfirmationModal/ReScheduleConfirmationModal";

interface IParams<T, ICart extends IPostpayCartModel<T>> {
    nextStepUrl?: string;
    prevStepUrl?: string;
    prospect: IProspect;
    customerInfo: ITigoSalesProspectLead;
    cart: ICart;
    setCustomerInfo: KeyedMutator<ITigoSalesProspectLead>;
}

const TASK_TYPE_NEW_SUBSCRIPTION = process.env.TASK_TYPE_NEW_SUBSCRIPTION || "";

export const ScheduleInstallationStep = <T, ICart extends IPostpayCartModel<T>>(props: IParams<T, ICart>) => {
    const { prevStepUrl, nextStepUrl, prospect, customerInfo, setCustomerInfo } = props;
    const { t } = useTranslation(["acquisition", "appointment", "address", "common"]);
    const { push } = useHistory();
    const [showSuccessModal, setShowSuccessModal] = useState(false);

    const installationAddress = customerInfo.address?.find(
        (val) => val.addressType === EAddressTypeValues.INSTALLATION
    );

    const taskType = TASK_TYPE_NEW_SUBSCRIPTION;

    const {
        departmentStr,
        municipalityStr,
        availableSlots,
        setAvailableSlots,
        selectedSlot,
        isLoading,
        procesarSlot,
        enableNextStep,
        handleNextStep,
        handleSlotSelection,
        modalTitle,
        isOpen,
        setIsOpen,
        handleConfirmAppointment,
        provinceStr,
        districtStr,
        townshipStr,
        neighborhoodStr,
        streetStr,
        houseStr,
        isErrorModalOpen,
        errorModalMessage,
        handleErrorModalConfirm,
        isSuccessModalOpen,
        setIsSuccessModalOpen,
    } = useScheduleInstallationStep({
        customerInfo,
        nextStepUrl,
        prospect,
        setCustomerInfo,
    });

    useEffect(() => {
        if (isSuccessModalOpen) {
            setShowSuccessModal(true);
        }
    }, [isSuccessModalOpen]);

    const handleCloseSuccessModal = () => {
        setShowSuccessModal(false);
        setIsSuccessModalOpen(false);
    };

    console.log("#### ScheduleInstallationStep ###  Propiedades",props);


    return (
        <StepLayout
            handleBack={() => push(prevStepUrl ?? "")}
            nextButtonProps={{
                handleNext: () => {
                    handleNextStep();
                },
                isDisabled: enableNextStep(),
            }}
            title={t("acquisition:scheduleInstallation")}
            showNextButton={false}
        >
            <LeavingGuard
                content={t("acquisition:leavingAcquisitionModalText")}
                reference={prospect.reference}
                title={t("acquisition:leavingAcquisition")}
            />

            {isLoading && <AbsoluteLoader />}
            <ScheduleForm
                availableSlots={availableSlots}
                callId={prospect.reference}
                departmentStr={departmentStr}
                districtStr={districtStr}
                handleConfirmAppointment={handleConfirmAppointment}
                handleSlotSelection={handleSlotSelection}
                houseStr={houseStr}
                installationAddress={installationAddress}
                isOpen={isOpen}
                modalTitle={modalTitle}
                municipalityStr={municipalityStr}
                neighborhoodStr={neighborhoodStr}
                procesarSlot={procesarSlot}
                provinceStr={provinceStr}
                selectedSlot={selectedSlot}
                setAvailableSlots={setAvailableSlots}
                setIsOpen={setIsOpen}
                streetStr={streetStr}
                taskType={taskType}
                townshipStr={townshipStr}
                isErrorModalOpen={isErrorModalOpen}
                errorModalMessage={errorModalMessage}
                handleErrorModalConfirm={handleErrorModalConfirm}
            />
            <ReScheduleConfirmationModal
                isOpen={showSuccessModal}
                onClose={handleCloseSuccessModal}
                message={t("common:scheduleSuccesfullMessage")}
                orderID={prospect.reference}
            />
        </StepLayout>
    );
};
