import { DialogTitleClose } from "@common/Dialog";
import { FormButton } from "@common/FormButton/FormButton";
import { CheckCircleOutlined } from "@mui/icons-material";
import { Box, Dialog, DialogActions, DialogContent, Stack, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useStyle } from "./style";

interface IParams {
    isOpen: boolean;
    onClose(): void;
    message: string;
    orderID?: string;
}

const ReScheduleConfirmationModal = (props: IParams) => {
    const { isOpen, onClose, message, orderID } = props;
    const { t } = useTranslation(["acquisition", "common", "changeOffer"]);
    const { classes } = useStyle();

    const handleBackHome = () => {
        onClose();
    };

    return (
            <Dialog data-testid="OrderConfirmationModal" fullWidth maxWidth="sm" open={isOpen}
                onClose={(_event, reason) => {
                    if (reason !== "backdropClick") {
                        handleBackHome();
                    }
                }}
            >
            <DialogTitleClose handleClose={handleBackHome} title={t("common:confirmation")} />
            <DialogContent>
                <Stack spacing={2}>
                    <Box textAlign="center">
                        <CheckCircleOutlined className={classes.icon} color="primary" />
                        <Typography align="center">{message}</Typography>
                        {orderID?.trim() && (
                            <Typography align="center">{t("acquisition:orderId")+": "+orderID}</Typography>
                        )}
                    </Box>
                </Stack>
            </DialogContent>
            <DialogActions>
                <FormButton
                    buttonText={t("acquisition:continue")}
                    color="primary"
                    variant="contained"
                    onClick={handleBackHome}
                />
            </DialogActions>
        </Dialog>
    );
};

export default ReScheduleConfirmationModal;
